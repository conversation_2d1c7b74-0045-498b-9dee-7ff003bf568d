from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON
from app import db

class BarkConfig(db.Model):
    """Bark配置模型"""
    __tablename__ = 'bark_configs'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)  # 配置名称
    device_key = Column(String(200), nullable=False)  # 设备key
    server_url = Column(String(500), default='https://api.day.app')  # Bark服务器地址
    
    # Bark参数配置
    default_title = Column(String(200))
    default_subtitle = Column(String(200))
    level = Column(String(20), default='active')  # critical/active/timeSensitive/passive
    volume = Column(Integer, default=5)  # 0-10
    badge = Column(Integer)
    call = Column(Boolean, default=False)
    auto_copy = Column(Boolean, default=False)
    copy_text = Column(String(500))
    sound = Column(String(100))
    icon = Column(String(500))
    group = Column(String(100))
    is_archive = Column(Boolean, default=True)
    url = Column(String(500))
    action = Column(String(20))  # none等
    
    # 状态
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<BarkConfig {self.name}: {self.device_key}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'device_key': self.device_key,
            'server_url': self.server_url,
            'default_title': self.default_title,
            'default_subtitle': self.default_subtitle,
            'level': self.level,
            'volume': self.volume,
            'badge': self.badge,
            'call': self.call,
            'auto_copy': self.auto_copy,
            'copy_text': self.copy_text,
            'sound': self.sound,
            'icon': self.icon,
            'group': self.group,
            'is_archive': self.is_archive,
            'url': self.url,
            'action': self.action,
            'is_active': self.is_active,
            'is_default': self.is_default,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def get_bark_params(self, title=None, subtitle=None, body=None):
        """获取Bark推送参数"""
        params = {
            'title': title or self.default_title or 'TradingView Alert',
            'body': body or '',
            'level': self.level,
            'volume': self.volume
        }
        
        if subtitle or self.default_subtitle:
            params['subtitle'] = subtitle or self.default_subtitle
        if self.badge is not None:
            params['badge'] = self.badge
        if self.call:
            params['call'] = '1'
        if self.auto_copy:
            params['autoCopy'] = '1'
        if self.copy_text:
            params['copy'] = self.copy_text
        if self.sound:
            params['sound'] = self.sound
        if self.icon:
            params['icon'] = self.icon
        if self.group:
            params['group'] = self.group
        if self.is_archive:
            params['isArchive'] = '1'
        if self.url:
            params['url'] = self.url
        if self.action:
            params['action'] = self.action
            
        return params

class TelegramConfig(db.Model):
    """Telegram配置模型"""
    __tablename__ = 'telegram_configs'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)  # 配置名称
    bot_token = Column(String(200), nullable=False)  # Bot Token
    chat_id = Column(String(100), nullable=False)  # 聊天ID或频道ID
    chat_type = Column(String(20), default='private')  # private/group/channel
    
    # 消息格式配置
    message_template = Column(Text)  # 消息模板
    parse_mode = Column(String(20), default='HTML')  # HTML/Markdown/MarkdownV2
    disable_web_page_preview = Column(Boolean, default=False)
    disable_notification = Column(Boolean, default=False)
    
    # 状态
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<TelegramConfig {self.name}: {self.chat_id}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'bot_token': self.bot_token,
            'chat_id': self.chat_id,
            'chat_type': self.chat_type,
            'message_template': self.message_template,
            'parse_mode': self.parse_mode,
            'disable_web_page_preview': self.disable_web_page_preview,
            'disable_notification': self.disable_notification,
            'is_active': self.is_active,
            'is_default': self.is_default,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def format_message(self, title, subtitle=None, body=None, original_data=None):
        """格式化消息内容"""
        if self.message_template:
            # 使用自定义模板
            template_vars = {
                'title': title or '',
                'subtitle': subtitle or '',
                'body': body or '',
                'original_data': original_data or {}
            }
            try:
                return self.message_template.format(**template_vars)
            except:
                pass
        
        # 默认格式
        message_parts = []
        if title:
            message_parts.append(f"<b>{title}</b>")
        if subtitle:
            message_parts.append(f"<i>{subtitle}</i>")
        if body:
            message_parts.append(body)
            
        return '\n\n'.join(message_parts)
