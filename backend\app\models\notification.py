from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON
from app import db

class NotificationRecord(db.Model):
    """通知记录模型"""
    __tablename__ = 'notification_records'
    
    id = Column(Integer, primary_key=True)
    
    # 基本信息
    source = Column(String(50), nullable=False, default='tradingview')  # 通知来源
    webhook_id = Column(String(100), unique=True, nullable=False)  # webhook唯一标识
    
    # TradingView 原始数据
    original_data = Column(JSON, nullable=False)  # 原始JSON数据
    
    # 解析后的通知内容
    title = Column(String(200))
    subtitle = Column(String(200))
    body = Column(Text)
    
    # 转发状态
    bark_sent = Column(Boolean, default=False)
    telegram_sent = Column(Boolean, default=False)
    
    # 转发结果
    bark_result = Column(JSON)  # Bark转发结果详情
    telegram_result = Column(JSON)  # Telegram转发结果详情
    
    # 错误信息
    error_message = Column(Text)
    retry_count = Column(Integer, default=0)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    sent_at = Column(DateTime)  # 成功发送时间
    
    def __repr__(self):
        return f'<NotificationRecord {self.id}: {self.title}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'source': self.source,
            'webhook_id': self.webhook_id,
            'title': self.title,
            'subtitle': self.subtitle,
            'body': self.body,
            'bark_sent': self.bark_sent,
            'telegram_sent': self.telegram_sent,
            'bark_result': self.bark_result,
            'telegram_result': self.telegram_result,
            'error_message': self.error_message,
            'retry_count': self.retry_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'original_data': self.original_data
        }
    
    @classmethod
    def create_from_webhook(cls, webhook_data, webhook_id=None):
        """从webhook数据创建通知记录"""
        if webhook_id is None:
            webhook_id = f"tv_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}"
        
        # 解析TradingView数据
        title = webhook_data.get('title', 'TradingView Alert')
        subtitle = webhook_data.get('subtitle', '')
        body = webhook_data.get('message', webhook_data.get('text', ''))
        
        record = cls(
            webhook_id=webhook_id,
            original_data=webhook_data,
            title=title,
            subtitle=subtitle,
            body=body
        )
        
        return record
