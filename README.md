# TradingView Notification Forwarder

基于 Apprise 的 TradingView 通知转发系统，支持将 TradingView 的 JSON 格式通知转发到 Bark 和 Telegram。

## 功能特性

- 🔔 接收 TradingView Webhook 通知
- 📱 支持转发到多个 Bark 设备
- 💬 支持转发到 Telegram Bot 和频道
- 🎛️ 完整的 Bark 参数配置支持
- 🌐 响应式 Web 管理界面（支持 PC 和手机）
- 📊 通知历史记录查询
- 🗄️ SQLite 数据库存储
- 🔧 基于 Apprise 的统一通知框架

## 项目结构

```
bark2/
├── backend/                 # 后端 API
│   ├── app/
│   │   ├── __init__.py
│   │   ├── models/         # 数据库模型
│   │   ├── api/           # API 路由
│   │   ├── services/      # 业务逻辑
│   │   └── utils/         # 工具函数
│   ├── config.py          # 配置文件
│   ├── requirements.txt   # Python 依赖
│   └── run.py            # 启动文件
├── frontend/              # 前端界面
│   ├── static/           # 静态资源
│   ├── templates/        # HTML 模板
│   └── assets/          # 前端资源
├── database/             # 数据库文件
├── logs/                # 日志文件
├── docker-compose.yml   # Docker 部署
├── Dockerfile          # Docker 镜像
└── README.md
```

## 支持的 Bark 参数

- `title`: 推送标题
- `subtitle`: 推送副标题  
- `body`: 推送内容
- `device_key`: 设备key
- `device_keys`: key 数组，用于批量推送
- `level`: 推送中断级别 (critical/active/timeSensitive/passive)
- `volume`: 重要警告的通知音量 (0-10)
- `badge`: 推送角标
- `call`: 通知铃声重复播放
- `autoCopy`: 自动复制推送内容
- `copy`: 指定复制的内容
- `sound`: 自定义铃声
- `icon`: 自定义图标
- `group`: 消息分组
- `ciphertext`: 加密推送的密文
- `isArchive`: 保存推送
- `url`: 点击推送跳转的URL
- `action`: 点击推送行为
- `id`: 推送ID（用于更新）
- `delete`: 删除通知

## 快速开始

### 使用 Docker

```bash
docker-compose up -d
```

### 手动安装

1. 安装依赖
```bash
cd backend
pip install -r requirements.txt
```

2. 启动后端
```bash
python run.py
```

3. 访问管理界面
```
http://localhost:5000
```

## API 接口

### TradingView Webhook
```
POST /api/webhook/tradingview
```

### 配置管理
```
GET/POST /api/config/bark
GET/POST /api/config/telegram
```

### 历史记录
```
GET /api/history
```

## 许可证

MIT License
